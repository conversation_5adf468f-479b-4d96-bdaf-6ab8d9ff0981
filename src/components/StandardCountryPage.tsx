import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import { useLanguage } from '../context/LanguageContext';
import EnhancedScholarshipCard from './EnhancedScholarshipCard';
import GreatYOPPagination from './GreatYOPPagination';


import AdPlacement from './AdPlacement';
import { Pagination, Spin, Alert } from 'antd';

interface Scholarship {
  id: number;
  title: string;
  description: string;
  level: string;
  country: string;
  deadline: string;
  isOpen: boolean;
  thumbnail: string;
  fundingSource?: string;
}

interface PaginationData {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

interface CountryPageConfig {
  country: string;
  title: string;
  description: string;
  keywords: string;
  infoTitle: string;
  infoContent: string;
  benefits: string[];
  apiEndpoint: string;
  flag?: string;
}

interface StandardCountryPageProps {
  config: CountryPageConfig;
}

const StandardCountryPage: React.FC<StandardCountryPageProps> = ({ config }) => {
  const { translations } = useLanguage();
  const [scholarships, setScholarships] = useState<Scholarship[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<PaginationData>({
    total: 0,
    page: 1,
    limit: 6,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false
  });

  // Handle scholarship card click
  const handleScholarshipClick = (id: number, slug?: string) => {
    if (slug) {
      window.location.href = `/bourse/${slug}`;
    } else {
      window.location.href = `/scholarships/${id}`;
    }
  };

  const fetchScholarships = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const params = new URLSearchParams({
        country: config.country,
        page: pagination.page.toString(),
        limit: pagination.limit.toString()
      });

      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}${config.apiEndpoint}?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch scholarships');
      }

      const data = await response.json();
      
      if (data.success) {
        setScholarships(data.data || []);
        setPagination(prev => ({
          ...prev,
          total: data.pagination?.total || 0,
          totalPages: data.pagination?.totalPages || 0,
          hasNextPage: data.pagination?.hasNextPage || false,
          hasPreviousPage: data.pagination?.hasPreviousPage || false
        }));
      } else {
        throw new Error(data.message || 'Failed to load scholarships');
      }
    } catch (error) {
      console.error('Error fetching scholarships:', error);
      setError('Impossible de charger les bourses. Veuillez réessayer plus tard.');
      setScholarships([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchScholarships();
  }, [pagination.page, config.country]);

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };



  return (
    <>
      <Helmet>
        <title>{config.title}</title>
        <meta name="description" content={config.description} />
        <meta name="keywords" content={config.keywords} />
      </Helmet>

      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
        {/* Hero Section - Compact and Centered */}
        <section className="relative overflow-hidden bg-gradient-to-br from-gray-950 via-gray-900 to-gray-800">
          {/* Decorative elements */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute -top-24 -right-24 w-96 h-96 rounded-full bg-white/5 blur-3xl"></div>
            <div className="absolute top-1/2 -left-24 w-80 h-80 rounded-full bg-white/5 blur-3xl"></div>
            <div className="absolute -bottom-32 left-1/2 transform -translate-x-1/2 w-2/3 h-48 bg-white/10 blur-3xl"></div>

            {/* Animated floating shapes */}
            <div className="absolute top-20 left-[10%] w-12 h-12 rounded-full bg-primary-light/20 animate-float"></div>
            <div className="absolute top-40 right-[15%] w-24 h-24 rounded-lg bg-primary-light/10 rotate-45 animate-float-delayed"></div>
            <div className="absolute bottom-20 left-[20%] w-16 h-16 rounded-lg bg-white/10 rotate-12 animate-float-slow"></div>
          </div>

          {/* Hero content */}
          <div className="relative z-10 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-8 md:pt-22 md:pb-10">
            <div className="text-center">
              {/* Centered Content */}
              <div className="max-w-4xl mx-auto">
                <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white leading-tight mb-4">
                  <span className="flex items-center justify-center gap-3 mb-2">
                    <span className="text-4xl">
                      {config.flag || '🌍'}
                    </span>
                    <span className="block">Bourses d'Études en {config.country}</span>
                  </span>
                </h1>

                <p className="text-lg text-blue-100 font-medium leading-relaxed mb-4">
                  Explorez un monde d'opportunités éducatives au cœur de l'excellence académique.
                </p>

                <p className="text-base text-white/90 leading-relaxed mb-6 max-w-3xl mx-auto">
                  Accédez aux meilleures bourses d'études pour poursuivre votre rêve académique en {config.country}.
                  Notre plateforme vous propose une sélection fiable et actualisée de financements, programmes
                  universitaires et offres adaptées à votre parcours et domaine d'études.
                </p>

                <div className="flex flex-col sm:flex-row items-center justify-center gap-6 text-white/80">
                  <div className="flex items-center gap-2">
                    <span className="text-xl">🎓</span>
                    <span>Trouvez la bourse qui vous correspond</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-xl">🌍</span>
                    <span>Étudiez dans un pays vibrant, innovant et accueillant</span>
                  </div>
                </div>
              </div>


            </div>
          </div>
        </section>

        {/* Desktop Ad - Only visible on large screens */}
        <div className="hidden lg:block py-6 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <AdPlacement
              adSlot="1234567890"
              adSize="leaderboard"
              responsive={true}
            />
          </div>
        </div>

        {/* Content Section */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="w-full">{/* Full width layout without sidebar */}

              {/* Scholarships Grid */}
              <div id="scholarships-section" className="mb-8">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-2xl font-bold text-gray-900">
                    Bourses Recommandées
                  </h2>
                  <div className="text-sm text-gray-600">
                    {!loading && !error && `${pagination.total} résultats`}
                  </div>
                </div>

                {loading ? (
                  <div className="flex justify-center items-center py-16">
                    <Spin size="large" tip="Chargement des bourses..." />
                  </div>
                ) : error ? (
                  <Alert
                    message="Erreur"
                    description={error}
                    type="error"
                    showIcon
                    className="mb-6 rounded-xl shadow-md"
                  />
                ) : (
                  <>
                    {/* Mobile Ad - Only visible on small screens */}
                    <div className="mb-8 md:hidden">
                      <AdPlacement
                        adSlot="4567890123"
                        adSize="rectangle"
                        responsive={true}
                        fullWidth={true}
                      />
                    </div>

                    <div className="gy-pcard-wrap">
                      {scholarships.map((scholarship, index) => (
                        <EnhancedScholarshipCard
                          key={scholarship.id}
                          id={scholarship.id}
                          title={scholarship.title}
                          thumbnail={scholarship.thumbnail}
                          deadline={scholarship.deadline}
                          isOpen={scholarship.isOpen}
                          level={scholarship.level}
                          country={scholarship.country}
                          fundingSource={scholarship.fundingSource}
                          onClick={handleScholarshipClick}
                          index={index}
                          variant="greatyop"
                        />
                      ))}
                    </div>

                    {/* GreatYOP Pagination */}
                    {pagination.total > 0 && (
                      <GreatYOPPagination
                        current={pagination.page}
                        total={pagination.total}
                        pageSize={pagination.limit}
                        onChange={handlePageChange}
                        showQuickJumper={false}
                      />
                    )}
                  </>
                )}
              </div>
            </div>
        </div>

        {/* Newsletter Section - Minimal */}
        <section className="relative py-4 overflow-hidden">
          {/* Background with gradient and pattern */}
          <div className="absolute inset-0 bg-gradient-to-br from-primary-dark to-primary">
            <div className="absolute inset-0 opacity-10">
              <svg width="100%" height="100%">
                <defs>
                  <pattern id="smallGrid" width="20" height="20" patternUnits="userSpaceOnUse">
                    <path d="M 20 0 L 0 0 0 20" fill="none" stroke="white" strokeWidth="0.5" />
                  </pattern>
                  <pattern id="grid" width="80" height="80" patternUnits="userSpaceOnUse">
                    <rect width="80" height="80" fill="url(#smallGrid)" />
                    <path d="M 80 0 L 0 0 0 80" fill="none" stroke="white" strokeWidth="1" />
                  </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#grid)" />
              </svg>
            </div>
          </div>

          {/* Decorative elements */}
          <div className="absolute top-0 right-0 -mt-20 -mr-20 w-80 h-80 rounded-full bg-white opacity-10 blur-3xl"></div>
          <div className="absolute bottom-0 left-0 -mb-20 -ml-20 w-80 h-80 rounded-full bg-white opacity-10 blur-3xl"></div>

          <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white rounded-lg shadow-lg overflow-hidden">
              <div className="grid grid-cols-1 lg:grid-cols-3">
                {/* Left column - Image */}
                <div className="relative hidden lg:block lg:col-span-1">
                  <img
                    src="/assets/newsletter-image.jpg"
                    alt="Student reading"
                    className="absolute inset-0 h-full w-full object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = 'https://images.unsplash.com/photo-1523240795612-9a054b0db644?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb&w=800';
                    }}
                  />
                  <div className="absolute inset-0 bg-gradient-to-r from-primary-dark/80 to-transparent mix-blend-multiply"></div>
                  <div className="absolute inset-0 flex items-center justify-center p-8">
                    <div className="text-white max-w-md">
                      <h3 className="text-2xl font-bold mb-4">Pourquoi s'abonner ?</h3>
                      <ul className="space-y-3">
                        {[
                          `Recevez les dernières bourses pour ${config.country}`,
                          'Soyez informé des dates limites importantes',
                          'Accédez à des conseils exclusifs pour vos candidatures',
                          'Découvrez des témoignages d\'étudiants boursiers'
                        ].map((benefit, index) => (
                          <li key={index} className="flex items-start">
                            <svg className="h-5 w-5 text-green-400 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                            <span>{benefit}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>

                {/* Right column - Form */}
                <div className="p-4 md:p-6 lg:col-span-2">
                  <div className="flex items-center mb-3">
                    <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 mr-2">
                      <svg className="w-4 h-4 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div>
                      <h2 className="text-lg font-bold text-gray-900">Newsletter</h2>
                      <p className="text-xs text-gray-600">Bourses d'études</p>
                    </div>
                  </div>

                  <p className="text-xs text-gray-600 mb-3">
                    Recevez les dernières opportunités de bourses directement dans votre boîte mail.
                  </p>

                  <form className="space-y-3">
                    <div className="flex gap-2">
                      <input
                        type="email"
                        placeholder="<EMAIL>"
                        className="flex-1 px-3 py-2 text-sm rounded-lg border border-gray-300 focus:ring-primary focus:border-primary focus:outline-none focus:ring-1 transition-colors duration-200"
                      />
                      <button
                        type="submit"
                        className="px-4 py-2 bg-primary text-white text-sm font-medium rounded-lg hover:bg-primary-dark transition-colors duration-300"
                      >
                        S'abonner
                      </button>
                    </div>

                    <div className="flex items-center">
                      <input
                        id="privacy"
                        type="checkbox"
                        className="h-3 w-3 text-primary focus:ring-primary border-gray-300 rounded"
                      />
                      <label htmlFor="privacy" className="ml-2 block text-xs text-gray-600">
                        J'accepte de recevoir des emails concernant les bourses d'études
                      </label>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </>
  );
};

export default StandardCountryPage;
