import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import { useLanguage } from '../context/LanguageContext';
import EnhancedScholarshipCard from './EnhancedScholarshipCard';
import GreatYOPPagination from './GreatYOPPagination';
import CountriesSidebar from './CountriesSidebar';

import AdPlacement from './AdPlacement';
import { Pagination, Spin, Alert } from 'antd';

interface Scholarship {
  id: number;
  title: string;
  description: string;
  level: string;
  country: string;
  deadline: string;
  isOpen: boolean;
  thumbnail: string;
  fundingSource?: string;
}

interface PaginationData {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

interface CountryPageConfig {
  country: string;
  title: string;
  description: string;
  keywords: string;
  infoTitle: string;
  infoContent: string;
  benefits: string[];
  apiEndpoint: string;
  flag?: string;
}

interface StandardCountryPageProps {
  config: CountryPageConfig;
}

const StandardCountryPage: React.FC<StandardCountryPageProps> = ({ config }) => {
  const { translations } = useLanguage();
  const [scholarships, setScholarships] = useState<Scholarship[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<PaginationData>({
    total: 0,
    page: 1,
    limit: 6,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false
  });

  // Handle scholarship card click
  const handleScholarshipClick = (id: number, slug?: string) => {
    if (slug) {
      window.location.href = `/bourse/${slug}`;
    } else {
      window.location.href = `/scholarships/${id}`;
    }
  };

  const fetchScholarships = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const params = new URLSearchParams({
        country: config.country,
        page: pagination.page.toString(),
        limit: pagination.limit.toString()
      });

      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}${config.apiEndpoint}?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch scholarships');
      }

      const data = await response.json();
      
      if (data.success) {
        setScholarships(data.data || []);
        setPagination(prev => ({
          ...prev,
          total: data.pagination?.total || 0,
          totalPages: data.pagination?.totalPages || 0,
          hasNextPage: data.pagination?.hasNextPage || false,
          hasPreviousPage: data.pagination?.hasPreviousPage || false
        }));
      } else {
        throw new Error(data.message || 'Failed to load scholarships');
      }
    } catch (error) {
      console.error('Error fetching scholarships:', error);
      setError('Impossible de charger les bourses. Veuillez réessayer plus tard.');
      setScholarships([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchScholarships();
  }, [pagination.page, config.country]);

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };



  return (
    <>
      <Helmet>
        <title>{config.title}</title>
        <meta name="description" content={config.description} />
        <meta name="keywords" content={config.keywords} />
      </Helmet>

      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 country-page">
        {/* Hero Section - Compact and Centered */}
        <section className="relative py-4 overflow-hidden">
          {/* Background with gradient and pattern */}
          <div className="absolute inset-0 bg-gradient-to-br from-primary-dark to-primary">
            <div className="absolute inset-0 opacity-10">
              <svg width="100%" height="100%">
                <defs>
                  <pattern id="heroSmallGrid" width="20" height="20" patternUnits="userSpaceOnUse">
                    <path d="M 20 0 L 0 0 0 20" fill="none" stroke="white" strokeWidth="0.5" />
                  </pattern>
                  <pattern id="heroGrid" width="80" height="80" patternUnits="userSpaceOnUse">
                    <rect width="80" height="80" fill="url(#heroSmallGrid)" />
                    <path d="M 80 0 L 0 0 0 80" fill="none" stroke="white" strokeWidth="1" />
                  </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#heroGrid)" />
              </svg>
            </div>
          </div>

          {/* Decorative elements */}
          <div className="absolute top-0 right-0 -mt-20 -mr-20 w-80 h-80 rounded-full bg-white opacity-10 blur-3xl"></div>
          <div className="absolute bottom-0 left-0 -mb-20 -ml-20 w-80 h-80 rounded-full bg-white opacity-10 blur-3xl"></div>

          {/* Hero content */}
          <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-12 pb-4 md:pt-14 md:pb-6">
            <div className="max-w-4xl">
              <h1 className="text-2xl font-bold text-white mb-4 leading-tight">
                Bourses d'Études en {config.country} : Guide Complet des Opportunités de Financement
              </h1>

              <p className="text-base text-white/90 leading-relaxed mb-4">
                Le système d'enseignement supérieur en {config.country} attire chaque année des milliers d'étudiants
                internationaux grâce à la qualité de ses institutions académiques et à la diversité de ses programmes
                de financement. Cette page présente une sélection rigoureuse des bourses d'études disponibles,
                incluant les critères d'éligibilité, les montants accordés et les procédures de candidature.
              </p>

              <p className="text-base text-white/85 leading-relaxed">
                Les opportunités de financement couvrent tous les cycles universitaires - licence, master et doctorat -
                et sont proposées par diverses institutions : gouvernements, universités publiques et privées,
                fondations internationales et organisations sectorielles. Chaque bourse est accompagnée d'informations
                détaillées sur les domaines d'études éligibles, les exigences linguistiques et les échéances de candidature
                pour faciliter votre processus de sélection et d'application.
              </p>
            </div>
          </div>
        </section>

        {/* Desktop Ad - Only visible on large screens */}
        <div className="hidden lg:block py-6 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <AdPlacement
              adSlot="1234567890"
              adSize="leaderboard"
              responsive={true}
            />
          </div>
        </div>

        {/* Content Section */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-3">{/* Main content area */}

              {/* Scholarships Grid */}
              <div id="scholarships-section" className="mb-8">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-2xl font-bold text-gray-900">
                    Bourses d'Études en {config.country}
                  </h2>
                  <div className="text-sm text-gray-600">
                    {!loading && !error && `${pagination.total} résultats`}
                  </div>
                </div>

                {loading ? (
                  <div className="flex justify-center items-center py-16">
                    <Spin size="large" tip="Chargement des bourses..." />
                  </div>
                ) : error ? (
                  <Alert
                    message="Erreur"
                    description={error}
                    type="error"
                    showIcon
                    className="mb-6 rounded-xl shadow-md"
                  />
                ) : (
                  <>
                    {/* Mobile Ad - Only visible on small screens */}
                    <div className="mb-8 md:hidden">
                      <AdPlacement
                        adSlot="4567890123"
                        adSize="rectangle"
                        responsive={true}
                        fullWidth={true}
                      />
                    </div>

                    <div className="gy-pcard-wrap">
                      {scholarships.map((scholarship, index) => (
                        <EnhancedScholarshipCard
                          key={scholarship.id}
                          id={scholarship.id}
                          title={scholarship.title}
                          thumbnail={scholarship.thumbnail}
                          deadline={scholarship.deadline}
                          isOpen={scholarship.isOpen}
                          level={scholarship.level}
                          country={scholarship.country}
                          fundingSource={scholarship.fundingSource}
                          onClick={handleScholarshipClick}
                          index={index}
                          variant="greatyop"
                        />
                      ))}
                    </div>

                    {/* GreatYOP Pagination */}
                    {pagination.total > 0 && (
                      <GreatYOPPagination
                        current={pagination.page}
                        total={pagination.total}
                        pageSize={pagination.limit}
                        onChange={handlePageChange}
                        showQuickJumper={false}
                      />
                    )}
                  </>
                )}
              </div>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <CountriesSidebar
                currentCountry={config.country}
                className="sticky top-8"
              />
            </div>
          </div>
        </div>

        {/* Newsletter Section - Compact and Professional */}
        <section className="relative py-8 overflow-hidden">
          {/* Background with gradient and pattern */}
          <div className="absolute inset-0 bg-gradient-to-br from-primary-dark to-primary">
            <div className="absolute inset-0 opacity-10">
              <svg width="100%" height="100%">
                <defs>
                  <pattern id="smallGrid" width="20" height="20" patternUnits="userSpaceOnUse">
                    <path d="M 20 0 L 0 0 0 20" fill="none" stroke="white" strokeWidth="0.5" />
                  </pattern>
                  <pattern id="grid" width="80" height="80" patternUnits="userSpaceOnUse">
                    <rect width="80" height="80" fill="url(#smallGrid)" />
                    <path d="M 80 0 L 0 0 0 80" fill="none" stroke="white" strokeWidth="1" />
                  </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#grid)" />
              </svg>
            </div>
          </div>
          {/* Decorative elements */}
          <div className="absolute top-0 right-0 -mt-20 -mr-20 w-80 h-80 rounded-full bg-white opacity-10 blur-3xl"></div>
          <div className="absolute bottom-0 left-0 -mb-20 -ml-20 w-80 h-80 rounded-full bg-white opacity-10 blur-3xl"></div>

          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white rounded-xl shadow-lg overflow-hidden">
              <div className="grid grid-cols-1 lg:grid-cols-5">
                {/* Left column - Image */}
                <div className="relative hidden lg:block lg:col-span-2">
                  <img
                    src="/assets/newsletter-image.jpg"
                    alt="Student reading"
                    className="absolute inset-0 h-full w-full object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = 'https://images.unsplash.com/photo-1523240795612-9a054b0db644?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb&w=800';
                    }}
                  />
                  <div className="absolute inset-0 bg-gradient-to-r from-primary-dark/80 to-transparent mix-blend-multiply"></div>
                  <div className="absolute inset-0 flex items-center justify-center p-6">
                    <div className="text-white">
                      <h3 className="text-lg font-bold mb-3">Restez Informé</h3>
                      <ul className="space-y-2 text-sm">
                        {[
                          `Bourses pour ${config.country}`,
                          'Dates limites importantes',
                          'Conseils exclusifs',
                          'Témoignages d\'étudiants'
                        ].map((benefit, index) => (
                          <li key={index} className="flex items-start">
                            <svg className="h-4 w-4 text-green-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                            <span className="leading-tight">{benefit}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>

                {/* Right column - Form */}
                <div className="p-6 lg:col-span-3">
                  <div className="flex items-center mb-4">
                    <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10 mr-3">
                      <svg className="w-5 h-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div>
                      <h2 className="text-xl font-bold text-gray-900">Newsletter Bourses</h2>
                      <p className="text-sm text-gray-600">Opportunités d'études en {config.country}</p>
                    </div>
                  </div>

                  <p className="text-sm text-gray-600 mb-4 leading-relaxed">
                    Recevez les dernières opportunités de bourses pour {config.country} directement dans votre boîte mail.
                    Nous ne vous enverrons pas de spam.
                  </p>

                  <form className="space-y-4">
                    <div className="flex gap-3">
                      <input
                        type="email"
                        placeholder="<EMAIL>"
                        className="flex-1 px-4 py-3 text-sm rounded-lg border border-gray-300 focus:ring-primary focus:border-primary focus:outline-none focus:ring-2 transition-colors duration-200"
                      />
                      <button
                        type="submit"
                        className="px-6 py-3 bg-primary text-white text-sm font-medium rounded-lg hover:bg-primary-dark transition-colors duration-300 whitespace-nowrap"
                      >
                        S'abonner
                      </button>
                    </div>

                    <div className="flex items-start">
                      <input
                        id="privacy"
                        type="checkbox"
                        className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded mt-0.5"
                      />
                      <label htmlFor="privacy" className="ml-3 block text-sm text-gray-600 leading-relaxed">
                        J'accepte de recevoir des emails concernant les bourses d'études et opportunités académiques
                      </label>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </>
  );
};

export default StandardCountryPage;
