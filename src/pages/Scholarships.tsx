import React, { useState, useEffect } from 'react';
import { Pagination, Spin, Alert } from 'antd';
import { useLocation } from 'react-router-dom';
import EnhancedScholarshipCard from '../components/EnhancedScholarshipCard';
import GreatYOPPagination from '../components/GreatYOPPagination';

import PageEndSuggestions from '../components/PageEndSuggestions';
import AdPlacement from '../components/AdPlacement';
import StandardizedFilters from '../components/filters/StandardizedFilters';

interface Scholarship {
  id: number;
  title: string;
  description: string;
  level: string;
  country: string;
  deadline: string;
  isOpen: boolean;
  thumbnail: string;
}

interface PaginationData {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

const Scholarships: React.FC = () => {
  const location = useLocation();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedLevel, setSelectedLevel] = useState('');
  const [selectedCountry, setSelectedCountry] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [scholarships, setScholarships] = useState<Scholarship[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<PaginationData>({
    total: 0,
    page: 1,
    limit: 6, // Show 6 scholarships per page (3x2 grid)
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false
  });

  // Read URL parameters on component mount
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const levelParam = searchParams.get('level');
    const countryParam = searchParams.get('country');

    if (levelParam) {
      setSelectedLevel(levelParam);
    }
    if (countryParam) {
      setSelectedCountry(countryParam);
    }
  }, [location.search]);

  // Fetch scholarships with pagination and filters
  useEffect(() => {
    fetchScholarships();
  }, [pagination.page, selectedLevel, selectedCountry, searchQuery]);

  const fetchScholarships = async () => {
    try {
      setLoading(true);

      // Build query parameters
      const params = new URLSearchParams();
      params.append('page', pagination.page.toString());
      params.append('limit', pagination.limit.toString());

      if (searchQuery) {
        params.append('q', searchQuery);
      }

      if (selectedLevel) {
        params.append('level', selectedLevel);
      }

      if (selectedCountry) {
        params.append('country', selectedCountry);
      }

      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/api/scholarships/search?${params.toString()}`);

      if (!response.ok) {
        throw new Error('Failed to fetch scholarships');
      }

      const data = await response.json();
      console.log('Scholarships search API response:', data);

      // Handle the correct API response format: { success: true, data: [...], pagination: {...} }
      const scholarshipsData = data.data || data.scholarships || [];
      const paginationData = data.pagination || {};

      setScholarships(scholarshipsData);
      setPagination(paginationData || {
        total: scholarshipsData.length || 0,
        page: 1,
        limit: 6,
        totalPages: Math.ceil((scholarshipsData.length || 0) / 6),
        hasNextPage: false,
        hasPreviousPage: false
      });
      setError(null);
    } catch (err) {
      console.error('Error fetching scholarships:', err);
      setError('Failed to load scholarships. Please try again later.');
      setScholarships([]);
    } finally {
      setLoading(false);
    }
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setPagination(prev => ({
      ...prev,
      page
    }));
    // Scroll to top when page changes
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <div className="bg-white min-h-screen">
      {/* Hero Section - Main Title Only */}
      <div className="relative py-6 overflow-hidden">
        {/* Background with gradient and pattern */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary-dark to-primary">
          <div className="absolute inset-0 opacity-10">
            <svg width="100%" height="100%">
              <defs>
                <pattern id="scholarshipsSmallGrid" width="20" height="20" patternUnits="userSpaceOnUse">
                  <path d="M 20 0 L 0 0 0 20" fill="none" stroke="white" strokeWidth="0.5" />
                </pattern>
                <pattern id="scholarshipsGrid" width="80" height="80" patternUnits="userSpaceOnUse">
                  <rect width="80" height="80" fill="url(#scholarshipsSmallGrid)" />
                  <path d="M 80 0 L 0 0 0 80" fill="none" stroke="white" strokeWidth="1" />
                </pattern>
              </defs>
              <rect width="100%" height="100%" fill="url(#scholarshipsGrid)" />
            </svg>
          </div>
        </div>

        {/* Decorative elements */}
        <div className="absolute top-0 right-0 -mt-20 -mr-20 w-80 h-80 rounded-full bg-white opacity-10 blur-3xl"></div>
        <div className="absolute bottom-0 left-0 -mb-20 -ml-20 w-80 h-80 rounded-full bg-white opacity-10 blur-3xl"></div>

        <div className="max-w-7xl mx-auto pt-16 pb-2 px-4 sm:pt-18 sm:pb-3 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center">
            <div className="flex items-center justify-center mb-1">
              <div className="flex items-center justify-center w-6 h-6 bg-white/20 rounded-lg mr-2">
                <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              </div>
              <h1 className="text-base font-medium text-white sm:text-lg animate-fade-in">
                Toutes les Bourses d'Études Disponibles
              </h1>
            </div>
            <p className="text-sm text-blue-100 max-w-2xl mx-auto animate-fade-in leading-relaxed">
              Explorez notre collection complète d'opportunités de financement pour tous les niveaux d'études
            </p>
          </div>
        </div>
      </div>

      {/* Content Section - Professional Format */}
      <div className="bg-white py-8 md:py-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <article>
            {/* Article Content - Full Width */}
            <div className="prose max-w-none">
              <p className="text-gray-700 leading-relaxed mb-6 text-base md:text-lg text-justify">
                Bienvenue sur la <strong>plateforme de référence</strong> pour les bourses d'études internationales.
                Cette page centralise toutes les <strong>opportunités de financement</strong> disponibles, soigneusement
                vérifiées et mises à jour quotidiennement pour vous offrir les meilleures chances de réussite dans vos
                projets académiques. Découvrez des bourses pour étudier en <strong>France</strong>, au <strong>Canada</strong>,
                aux <strong>États-Unis</strong>, en <strong>Allemagne</strong>, au <strong>Royaume-Uni</strong>, en
                <strong>Australie</strong> et dans de nombreux autres pays d'excellence académique.
              </p>

              <p className="text-gray-700 leading-relaxed text-base md:text-lg text-justify">
                Que vous souhaitiez poursuivre des études en <strong>Ingénierie</strong>, <strong>Médecine</strong>,
                <strong>Sciences Informatiques</strong>, <strong>Business</strong>, <strong>Sciences Sociales</strong>
                ou <strong>Arts</strong>, notre collection comprend des bourses pour tous les niveaux d'études - de la
                <strong>licence au doctorat</strong>. Chaque opportunité est accompagnée d'informations détaillées sur
                les critères d'éligibilité, les <strong>avantages financiers</strong>, les procédures de candidature
                et les échéances importantes. Notre <strong>système de recherche intelligent</strong> vous permettra
                de découvrir rapidement les bourses qui correspondent parfaitement à votre profil et à vos ambitions académiques.
              </p>
            </div>
          </article>
        </div>
      </div>

      {/* Ad Placement - Top Banner */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 -mt-4 mb-6 hidden md:block">
        <AdPlacement
          adSlot="1234567890"
          adSize="leaderboard"
          responsive={true}
          fullWidth={true}
        />
      </div>



      {/* Content Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
        {/* Main Content - Full Width */}
        <div className="w-full">


        {loading ? (
          <div className="flex justify-center items-center py-16">
            <Spin size="large" tip="Chargement des bourses..." />
          </div>
        ) : error ? (
          <Alert
            message="Erreur"
            description={error}
            type="error"
            showIcon
            className="mb-6 rounded-xl shadow-md"
          />
        ) : (
          <>
            {/* Mobile Ad - Only visible on small screens */}
            <div className="mb-8 md:hidden">
              <AdPlacement
                adSlot="2345678901"
                adSize="rectangle"
                responsive={true}
                fullWidth={true}
              />
            </div>

            <div className="gy-pcard-wrap">
              {scholarships.map((scholarship, index) => (
                <EnhancedScholarshipCard
                  key={scholarship.id}
                  id={scholarship.id}
                  title={scholarship.title}
                  thumbnail={scholarship.thumbnail}
                  deadline={scholarship.deadline}
                  isOpen={scholarship.isOpen}
                  level={scholarship.level}
                  country={scholarship.country}
                  onClick={(id, slug) => window.location.href = slug ? `/bourse/${slug}` : `/scholarships/${id}`}
                  index={index}
                  variant="greatyop"
                />
              ))}
            </div>

            {/* No Results Message */}
            {scholarships.length === 0 && (
              <div className="text-center py-16 bg-gray-50 rounded-2xl shadow-sm border border-gray-100">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <h3 className="mt-4 text-lg font-medium text-gray-900">Aucune bourse trouvée</h3>
                <p className="mt-2 text-sm text-gray-500 max-w-md mx-auto">
                  Essayez d'ajuster vos critères de recherche ou de filtrage pour trouver ce que vous cherchez.
                </p>
                <button
                  onClick={() => {
                    setSearchQuery('');
                    setSelectedLevel('');
                    setSelectedCountry('');
                    setPagination(prev => ({ ...prev, page: 1 }));
                  }}
                  className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                >
                  Réinitialiser les filtres
                </button>
              </div>
            )}

            {/* Pagination */}
            {pagination.total > 0 && (
              <GreatYOPPagination
                current={pagination.page}
                total={pagination.total}
                pageSize={pagination.limit}
                onChange={handlePageChange}
                showQuickJumper={false}
              />
            )}


          </>
        )}
        </div>
      </div>

        {/* Page End Suggestions */}
        <PageEndSuggestions
          currentPageType="scholarship"
          currentItem={selectedLevel || selectedCountry}
        />

        {/* Newsletter Section - Compact */}
        <section className="relative py-6 overflow-hidden">
          {/* Background with gradient and pattern */}
          <div className="absolute inset-0 bg-gradient-to-br from-primary-dark to-primary">
            <div className="absolute inset-0 opacity-10">
              <svg width="100%" height="100%">
                <defs>
                  <pattern id="newsletterSmallGrid" width="20" height="20" patternUnits="userSpaceOnUse">
                    <path d="M 20 0 L 0 0 0 20" fill="none" stroke="white" strokeWidth="0.5" />
                  </pattern>
                  <pattern id="newsletterGrid" width="80" height="80" patternUnits="userSpaceOnUse">
                    <rect width="80" height="80" fill="url(#newsletterSmallGrid)" />
                    <path d="M 80 0 L 0 0 0 80" fill="none" stroke="white" strokeWidth="1" />
                  </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#newsletterGrid)" />
              </svg>
            </div>
          </div>

          {/* Decorative elements */}
          <div className="absolute top-0 right-0 -mt-20 -mr-20 w-80 h-80 rounded-full bg-white opacity-10 blur-3xl"></div>
          <div className="absolute bottom-0 left-0 -mb-20 -ml-20 w-80 h-80 rounded-full bg-white opacity-10 blur-3xl"></div>

          <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white rounded-xl shadow-lg overflow-hidden">
              <div className="p-6 text-center">
                <div className="flex items-center justify-center mb-4">
                  <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10 mr-3">
                    <svg className="w-5 h-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div>
                    <h2 className="text-lg font-bold text-gray-900">Newsletter Bourses</h2>
                    <p className="text-sm text-gray-600">Restez informé des nouvelles opportunités</p>
                  </div>
                </div>

                <p className="text-sm text-gray-600 mb-4 max-w-2xl mx-auto">
                  Recevez les dernières bourses d'études directement dans votre boîte mail.
                  Soyez le premier informé des nouvelles opportunités de financement.
                </p>

                <form className="max-w-md mx-auto">
                  <div className="flex gap-3">
                    <input
                      type="email"
                      placeholder="<EMAIL>"
                      className="flex-1 px-4 py-2 text-sm rounded-lg border border-gray-300 focus:ring-primary focus:border-primary focus:outline-none focus:ring-2 transition-colors duration-200"
                    />
                    <button
                      type="submit"
                      className="px-6 py-2 bg-primary text-white text-sm font-medium rounded-lg hover:bg-primary-dark transition-colors duration-300 whitespace-nowrap"
                    >
                      S'abonner
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </section>
      </div>
    );
};

export default Scholarships;